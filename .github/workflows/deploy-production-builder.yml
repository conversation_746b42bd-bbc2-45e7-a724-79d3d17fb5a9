name: Deploy Production ThankView Envelope Builder
on:
  # Uncomment this section if you want to deploy automatically on push to main/production
  # push:
  #   branches:
  #     - production
  #   paths:
  #     - 'thankview-deploy-production/**'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to (always production)'
        required: true
        default: 'production'
        type: string
      deploy_target:
        description: 'Server group to deploy to'
        required: true
        default: 'builder'
        type: choice
        options:
          - builder
      deploy_branch:
        description: 'Git branch to deploy (Please enter the branch name to deploy from)'
        required: true
        default: 'production'
        type: string

jobs:
  deploy:
    name: Deploy Envelope Builder
    runs-on: [self-hosted, Linux, X64, ec2, prod]
    
    env:
      AWS_ACCESS_KEY_ID: ${{ secrets.PRODUCTION_AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.PRODUCTION_AWS_SECRET_ACCESS_KEY }}
      AWS_REGION: us-east-1
      DEPLOY_ENV: 'production'
    
    steps:
      - name: Checkout code (always from production branch for playbooks)
        uses: actions/checkout@v4
        with:
          ref: production
          
      - name: List deployment directory content
        run: |
          echo "=== Deployment files from production branch ==="
          ls -la thankview-deploy-production || echo "Directory not found"
          echo ""
          echo "=== Verifying playbook files ==="
          find thankview-deploy-production -name "*.yml" -type f 2>/dev/null || echo "No playbook files found"
          
      - name: Verify System Python
        run: |
          python3 --version
          python3 -m pip install --upgrade pip
          
      - name: Install Ansible and dependencies
        run: |
          python3 -m pip install --upgrade pip
          python3 -m pip install --user ansible boto3 botocore
          echo 'export PATH=$HOME/.local/bin:$PATH' >> ~/.bashrc
          source ~/.bashrc
          export PATH=$HOME/.local/bin:$PATH
          which ansible
          which ansible-galaxy
          ansible-galaxy collection install amazon.aws community.general community.aws --force
          
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.PRODUCTION_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.PRODUCTION_AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
          
      - name: Set up SSH keys
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.PRODUCTION_DEPLOY_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -t rsa github.com >> ~/.ssh/known_hosts
          
      - name: Run inventory check
        working-directory: ${{ github.workspace }}/thankview-deploy-production
        run: |
          export PATH=$HOME/.local/bin:$PATH
          $HOME/.local/bin/ansible-inventory --graph -i aws_ec2.aws_ec2.yml
        
      - name: Deploy - Builder Servers
        working-directory: ${{ github.workspace }}/thankview-deploy-production
        run: |
          export PATH=$HOME/.local/bin:$PATH
          $HOME/.local/bin/ansible-playbook -i aws_ec2.aws_ec2.yml builder-deploy.yml -e "deploy_env=production deploy_branch=${{ github.event.inputs.deploy_branch || 'production' }}"
        
      # Uncomment if you want to add notifications
      # - name: Send notification
      #   if: always()
      #   working-directory: ${{ github.workspace }}/thankview-deploy-production
      #   run: |
      #     export PATH=$HOME/.local/bin:$PATH
      #     DEPLOY_TARGET="builder"
      #     DEPLOY_STATUS="${{ job.status }}"
      #     ansible-playbook -i aws_ec2.aws_ec2.yml notification.yml -e "deploy_env=$DEPLOY_ENV deploy_target=$DEPLOY_TARGET deploy_status=$DEPLOY_STATUS"
      #   continue-on-error: true