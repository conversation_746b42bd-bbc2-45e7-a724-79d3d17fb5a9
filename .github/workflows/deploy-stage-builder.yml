name: Deploy ThankView Envelope Builder
on:
  # Uncomment this section if you want to deploy automatically on push to main/staging
  # push:
  #   branches:
  #     - staging
  #   paths:
  #     - 'thankview-deploy-stage/**'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to (always staging)'
        required: true
        default: 'stage'
        type: string
      deploy_target:
        description: 'Server group to deploy to'
        required: true
        default: 'builder'
        type: choice
        options:
          - builder
      deploy_branch:
        description: 'Git branch to deploy (Please enter the branch name to deploy from)'
        required: true
        default: 'staging'
        type: string
jobs:
  deploy:
    name: Deploy Envelope Builder
    runs-on: [self-hosted, Linux, X64, ec2, builder]
    env:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      AWS_REGION: us-east-1
      DEPLOY_ENV: 'stage'
    steps:
      - name: Checkout code from selected branch
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.deploy_branch || 'staging' }}
          
      - name: Fetch deployment scripts from staging
        run: |
          if [ "${{ github.event.inputs.deploy_branch }}" != "staging" ]; then
            echo "Fetching deployment scripts from staging branch..."
            git fetch origin staging
            git checkout origin/staging -- thankview-deploy-stage || echo "Warning: Could not fetch thankview-deploy-stage from staging branch"
          fi
          
      - name: List directory content
        run: |
          echo "Current directory structure:"
          ls -la
          echo "Checking for deployment directory:"
          ls -la thankview-deploy-stage || echo "Directory not found"
          
      - name: Verify System Python
        run: |
          python3 --version
          python3 -m pip install --upgrade pip
          
      - name: Install Ansible and dependencies
        run: |
          python3 -m pip install --upgrade pip
          python3 -m pip install --user ansible boto3 botocore
          echo 'export PATH=$HOME/.local/bin:$PATH' >> ~/.bashrc
          source ~/.bashrc
          export PATH=$HOME/.local/bin:$PATH
          which ansible
          which ansible-galaxy
          ansible-galaxy collection install amazon.aws community.general community.aws --force
          
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
          
      - name: Set up SSH keys
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.DEPLOY_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -t rsa github.com >> ~/.ssh/known_hosts
          
      - name: Run inventory check
        working-directory: ${{ github.workspace }}/thankview-deploy-stage
        run: ansible-inventory --graph -i aws_ec2.aws_ec2.yml
        
      - name: Deploy - Builder Servers
        working-directory: ${{ github.workspace }}/thankview-deploy-stage
        run: ansible-playbook -i aws_ec2.aws_ec2.yml builder-deploy.yml -e "deploy_env=stage deploy_branch=${{ github.event.inputs.deploy_branch || 'staging' }}"
        
      # Uncomment if you want to add notifications
      # - name: Send notification
      #   if: always()
      #   working-directory: ${{ github.workspace }}/thankview-deploy-stage
      #   run: |
      #     DEPLOY_TARGET="builder"
      #     DEPLOY_STATUS="${{ job.status }}"
      #     ansible-playbook -i aws_ec2.aws_ec2.yml notification.yml -e "deploy_env=$DEPLOY_ENV deploy_target=$DEPLOY_TARGET deploy_status=$DEPLOY_STATUS"
      #   continue-on-error: true