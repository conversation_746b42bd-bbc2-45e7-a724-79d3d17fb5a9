# ThankView Envelope Builder - Production Deployment

This directory contains the Ansible playbooks and configuration for deploying the ThankView Envelope Builder application to production infrastructure.

## 🏗️ Architecture Overview

```mermaid
graph TB
    subgraph "GitHub Actions"
        GHA[GitHub Actions Workflow<br/>deploy-production-builder.yml]
    end
    
    subgraph "Self-Hosted Runner"
        SHR[Self-Hosted Linux Runner<br/>prod tag]
        ANS[Ansible Engine]
        SHR --> ANS
    end
    
    subgraph "AWS Infrastructure"
        subgraph "EC2 Instances"
            BUILDER[Builder Servers<br/>tag: prod-builder]
        end
        
        subgraph "AWS Services"
            SSM[Systems Manager<br/>Parameter Store]
            SEC[Secrets Manager<br/>Environment Variables]
        end
    end
    
    GHA --> SHR
    ANS --> BUILDER
    ANS --> SSM
    ANS --> SEC
```

## 📁 Directory Structure

```
thankview-deploy-production/
├── README.md                    # This file
├── ansible.cfg                  # Ansible configuration
├── aws_ec2.aws_ec2.yml         # AWS EC2 dynamic inventory
├── builder-deploy.yml          # Builder server deployment playbook
└── main-deploy.yml             # Main orchestration playbook
```

## 🚀 Deployment Process

### Automated Deployment (Recommended)

1. **Navigate to GitHub Actions**
   - Go to the repository's Actions tab
   - Select "Deploy Production ThankView Envelope Builder"

2. **Configure Deployment**
   - **Environment**: `production` (fixed)
   - **Deploy Target**: `builder` (fixed)
   - **Deploy Branch**: Enter the branch name to deploy (default: `production`)

3. **Execute Deployment**
   - Click "Run workflow"
   - Monitor the deployment progress in the Actions tab

### Manual Deployment (Advanced Users)

```bash
# From the thankview-deploy-production directory
ansible-playbook -i aws_ec2.aws_ec2.yml builder-deploy.yml -e "deploy_env=production deploy_branch=main"
```

## 🔧 Deployment Flow

```mermaid
sequenceDiagram
    participant User
    participant GHA as GitHub Actions
    participant Runner as Self-Hosted Runner
    participant AWS as AWS Services
    participant Builder as Builder Servers

    User->>GHA: Trigger deployment workflow
    GHA->>Runner: Execute on self-hosted runner
    Runner->>Runner: Install Ansible & dependencies
    Runner->>AWS: Configure AWS credentials
    Runner->>AWS: Query EC2 instances (dynamic inventory)
    Runner->>Builder: Deploy application code
    Runner->>AWS: Fetch SSH keys from Parameter Store
    Runner->>AWS: Fetch environment variables from Secrets Manager
    Runner->>Builder: Update application dependencies
    Runner->>Builder: Restart PHP-FPM service
    Runner->>GHA: Report deployment status
```

## 📋 What Gets Deployed

### Application Deployment Steps

1. **Code Deployment**
   - Clones/updates from specified Git branch
   - Resets repository to clean state
   - Pulls latest code changes

2. **Dependency Management**
   - Runs `composer update` to install PHP dependencies
   - Executes `composer dump-autoload` for optimized autoloading

3. **Configuration Management**
   - Fetches environment variables from AWS Secrets Manager
   - Updates `.env` files across multiple application directories
   - Sets `APP_ROLE` based on EC2 instance tags

4. **Service Management**
   - Restarts PHP-FPM service to apply changes

## 🎯 Target Infrastructure

### Server Groups

- **Builder Servers**: EC2 instances tagged with `prod-builder`
  - Hosts the ThankView Envelope Builder application
  - Runs PHP 8.1 with FPM
  - Uses Ubuntu as the base OS

### AWS Integration

- **Dynamic Inventory**: Automatically discovers EC2 instances using tags
- **Parameter Store**: Stores SSH keys for secure Git access
- **Secrets Manager**: Manages environment variables and sensitive configuration

## 🔐 Security & Access

### Authentication
- Uses AWS IAM credentials for service access
- SSH keys stored in AWS Systems Manager Parameter Store
- Private key authentication for Git repository access

### Network Access
- Connects to EC2 instances via private IP addresses
- Uses SSH with key-based authentication
- Disables host key checking for automation

## 📊 Monitoring & Troubleshooting

### Deployment Logs
- All deployment steps are logged in GitHub Actions
- Ansible provides detailed task execution information
- Failed deployments include error details and stack traces

### Common Issues

1. **Ansible Command Not Found**
   - Ensure PATH includes `~/.local/bin`
   - Verify Ansible installation completed successfully

2. **SSH Connection Failures**
   - Check SSH key permissions (600)
   - Verify security group allows SSH access from runner

3. **Composer Failures**
   - Review PHP version compatibility
   - Check for missing system dependencies

### Health Checks
```bash
# Verify inventory discovery
ansible-inventory --graph -i aws_ec2.aws_ec2.yml

# Test connectivity to builder servers
ansible builder -i aws_ec2.aws_ec2.yml -m ping

# Check application status
ansible builder -i aws_ec2.aws_ec2.yml -m shell -a "systemctl status php8.1-fpm"
```

## 🔄 Rollback Procedures

In case of deployment issues:

1. **Quick Rollback**
   - Re-run deployment with previous stable branch
   - Monitor application logs for errors

2. **Manual Intervention**
   - SSH to affected servers
   - Manually checkout previous commit
   - Restart services as needed

## 📝 Configuration Files

### ansible.cfg
- Sets default inventory and connection parameters
- Configures SSH options for automation
- Defines remote user and key file locations

### aws_ec2.aws_ec2.yml
- Dynamic inventory configuration for AWS EC2
- Filters instances by environment tags
- Groups servers by function and role

## 🚨 Important Notes

- **Branch Strategy**: Workflow definitions always run from `production` branch
- **Environment**: Currently configured for `prod-test` environment tags
- **Dependencies**: Requires `amazon.aws` and `community.general` Ansible collections
- **Permissions**: Runner must have appropriate AWS IAM permissions for EC2, SSM, and Secrets Manager

## 📞 Support

For deployment issues or questions:
1. Check GitHub Actions logs for detailed error information
2. Review Ansible task output for specific failure points
3. Verify AWS service permissions and connectivity
4. Contact the DevOps team for infrastructure-related issues
